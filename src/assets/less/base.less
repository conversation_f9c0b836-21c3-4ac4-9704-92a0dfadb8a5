.layout {
  height: 100% !important;
  min-height: 100% !important;
  overflow: hidden;
}

.sider.light .logo {
  padding-left: 40px;
  min-width: 600px;

  a {
    .logo-img {
      width: 40px;
      height: 33px;
      background: url('~@/assets/logo.png') no-repeat center / 100% 100%;
      float: left;
      margin: 15px 10px 0 0;
    }

    h1 {
      float: left;
      white-space: nowrap;
      line-height: 64px;
      font-size: 24px;
      color: #ffffff;
      font-weight: bold;

      &>i {
        position: relative;
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ffffff;
        margin: 0 10px;
        top: -3px;
      }

      &>span {
        font-size: 22px;
        font-weight: normal;
      }
    }
  }
}

.layout .header {
  height: 64px !important;
  line-height: 64px  !important;
}

.layout .header, .sider.light .logo
 {
  background: #3171EE !important;
}

.layout .header .user-wrapper {
  overflow: hidden;
  z-index: 9999999;
}

.layout .header .user-wrapper .action,
.layout .top-nav-header-index .user-wrapper .action {
  padding: 0 25px !important;
}

.layout .header .user-wrapper .action .avatar {
  width: 40px;
  height: 40px;
  margin: 12px 10px 9px 0;
  float: left;
}

.layout .header .user-wrapper .user-info {
  float: left;
  font-size: 16px;
  line-height: 64px;
  // padding-top: 10px;
  text-align: CENTER;

  &>b {
    font-weight: normal;
    font-size: 12px;
  }
}

.user-dropdown-menu-wrapper.ant-dropdown-menu {
  .ant-dropdown-menu-item {
    width: auto;
  }
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
  background-color: rgba(61,127,255,.1) !important;
}

.ant-menu-vertical .ant-menu-item::after, .ant-menu-vertical-left .ant-menu-item::after, .ant-menu-vertical-right .ant-menu-item::after, .ant-menu-inline .ant-menu-item::after {
  border-right: 6px solid #3D7FFF !important;
}

.ant-menu-submenu-selected, .ant-menu-item-selected > a, .ant-menu-item-selected > a:hover {
  color:  #3D7FFF !important;
}

.ant-layout.sidemenu .tab-layout-tabs.ant-tabs {
  border-left: none;
}

.ant-btn {
  margin-left: 0;
  margin-right: 10px;
}

.ant-btn-primary {
  background: #3171EE !important;
  color: #ffffff !important;
}

.ant-btn-primary.ant-btn-background-ghost {
  background: #3171EE !important;
  color: #ffffff !important;
}

.ant-btn-primary-disabled, .ant-btn-primary.disabled, .ant-btn-primary[disabled], .ant-btn-primary-disabled:hover, .ant-btn-primary.disabled:hover, .ant-btn-primary[disabled]:hover, .ant-btn-primary-disabled:focus, .ant-btn-primary.disabled:focus, .ant-btn-primary[disabled]:focus, .ant-btn-primary-disabled:active, .ant-btn-primary.disabled:active, .ant-btn-primary[disabled]:active, .ant-btn-primary-disabled.active, .ant-btn-primary.disabled.active, .ant-btn-primary[disabled].active {
  background-color: #f5f5f5  !important;
}

.ant-alert-info {
  background: #DBE7FF !important;
  border: 1px solid #3171EE !important;
}

.ant-table-pagination.ant-pagination {
  float: none !important;
  text-align: center !important;
  margin: 16px 0 0 0 !important;

  .ant-pagination-item,.ant-pagination-prev, .ant-pagination-next  {
    background-color: #DDDDDD !important;
    border: 1px solid #DDDDDD !important;
    min-width: 30px !important;
    height: 30px !important;
    line-height: 28px !important;
    margin: 0 4px !important;

    &.ant-pagination-item-active {
      background-color: #3D7FFF !important;

      &>a {
        color: #ffffff !important;
      }
    }
  }

  .ant-pagination-prev, .ant-pagination-next {
    background-color: #DDDDDD !important;
    border: 1px solid #DDDDDD !important;
  }

  .ant-select-sm .ant-select-selection--single {
    height: 30px !important;
    width: 90px;

    .ant-select-selection__rendered {
      line-height: 30px !important;
    }
  }

  .ant-pagination-options-quick-jumper {
    height: 30px !important;
    line-height: 30px !important;

    &>input {
      height: 30px !important;
    }
  }
}

.ant-card-body .table-operator {
  margin-bottom: 8px;

  .ant-btn {
    margin: 0 8px 8px 0;
  }
}

.ant-layout-sider-children{
  .ant-menu.ant-menu-inline.ant-menu-root {
    padding: 0px;
    height: calc(100% - 59px);
    overflow: auto;
  }
}

.ant-select-selection-selected-value {
  position: absolute;
}

.common-container-map {
  margin-right: 10px;
  display: flex;
  flex-direction: row;

  .show-map-btn {
    position: absolute;
    width: 18px;
    height: 110px;
    cursor: pointer;
    right: -5px;
    top: 50%;
    background: url('~@/assets/map-show.png');
    z-index: 1;
    transition: all 200ms linear;
  }

  .show-map-btn:hover{
    transform: scale(1.3);
  }

  .hide-map-btn {
    position: absolute;
    width: 18px;
    height: 110px;
    cursor: pointer;
    left: 0px;
    top: 50%;
    background: url('~@/assets/map-hide.png');
    z-index: 1;
  }

  .ant-card {
    transition: all .5s;
    width: 100%;

    &:first-of-type {
      overflow: auto;

      &>.ant-card-body {
        min-width: 878px;
      }
    }

    &:nth-child(2) {
      flex: 1;
      width: 0;
      margin-right: -10px;

      .ant-card-body {
        padding: 10px;
        height: 100%;

        .map-container {
          position: relative;
          width: 100%;
          height: 100%;
          background-color: #2F77F2;
        }
      }
    }

    &.show-map {
      width: 70%;
    }
  }
}

.right-total {
  width: auto;
  float: right;
  color: #292F36 !important;

  &>label {
    color: #599cff;
    font-weight: 600;
  }
}


// 事件要素菜单限制树组件最大高度
.event-warehouse {
  .ant-tree {
    max-height: 600px;
    overflow: auto;
  }
}

// 地图信息窗样式完善
.amap-info-content {
  max-width: 300px;
  word-break: break-all;
}

.ant-cascader-menu {
  height: auto !important;
}
.ant-layout-sider{
  position: relative;
  z-index: 99;
}
/* 滚动条优化 start */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background: #F5F5F5;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb {
  background: #D8D8D8;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover{
  background: #C5C5C5;
}
::-webkit-scrollbar-corner {
  background: #F8F8F8;
}
/* 滚动条优化 end */

/* 页面布局 start */
@pageWidth: 100%;
.common-scroll-box {
  width: @pageWidth !important;
}
.layout {
  .top-nav-header-index {
    .top-content {
      width: @pageWidth !important;
      .logo-box {
        margin-left: 20px;
      }
    }
    .header-index-wide {
      .ant-menu.ant-menu-horizontal {
        width: @pageWidth !important;
      }
    }
  }

  .ant-layout-content {
    padding: 0 10px !important;
    overflow-y: auto !important;
    .tab-layout-tabs.ant-tabs {
      border-left: none;
      border-bottom: none;
    }
  }
}
/* 页面布局 end */
.ant-table-thead > tr > th {
  background: #F6F8FD !important;
}
.common-heading {
  margin: 20px 0;
  display: flex;
  align-items: center;
  color: #333333;
  line-height: 24px;
  font-weight: 400;
}
h3.common-heading {
  margin-left: -16px;
  font-size: 16px;

  &::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 18px;
    background: #3171EE;
    margin-right: 12px;
  }
}
h4.common-heading {
  margin-top: 0;
  font-size: 14px;
}
.ant-divider-horizontal {
  margin: 16px 0 !important;
  background: #EEEEEE !important;
}
.ant-form-item-label > label {
  color: #666666 !important;
}

//全局表格样式
.ant-table-thead > tr > th {
  // text-align: center !important;
  font-size: 14px;
  color: #747474 !important;
  background:  #F6F8FD !important;
}
.ant-table-tbody > tr > td {
  color: #424242 !important;
  font-weight: 400 !important;
  font-size: 14px !important;
}
.ant-pagination.mini .ant-pagination-item,
.ant-pagination.mini .ant-pagination-next,
.ant-pagination.mini .ant-pagination-item-link {
  background-color: #f0f2f5 !important;
}

.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: none !important;
}

/* 自定义必填 start */
.customRequire {
  .ant-form-item {
    margin-bottom: 1px !important;
  }
  .opacity0 {
    opacity: 0;
  }
}

.iclient-leaflet-logo {
  display: none;
}