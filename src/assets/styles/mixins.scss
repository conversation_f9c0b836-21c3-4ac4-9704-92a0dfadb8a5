// 现代化 Sass mixins 模块 - mixins 定义文件
// 注意：在 Sass 模块系统中，mixins 会自动可用，不需要 @forward

// 操作栏 mixin
@mixin action-bar {
  .action-bar {
    height: 39px;
    background: #f5f7fa;
    padding: 0 15px;
    box-sizing: border-box;

    .bar-btn {
      display: inline-block;
      padding: 0 6px;
      line-height: 32px;
      color: #8285f5;
      cursor: pointer;
      font-size: 14px;
      user-select: none;
      & i {
        font-size: 20px;
      }
      &:hover {
        color: #4348d4;
      }
    }
    .bar-btn + .bar-btn {
      margin-left: 8px;
    }
    .delete-btn {
      color: #f56c6c;
      &:hover {
        color: #ea0b30;
      }
    }
  }
}

// 清除浮动 mixin
@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

// 自定义滚动条样式 mixin
@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

// 相对定位布局 mixin
@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

// 百分比宽度布局 mixin
@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

// 三角形生成 mixin
@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}
