<!-- 改 -->
<template>
  <div>
    <x-form ref="xForm" v-model="formData" :config="formConfig">
      <template #executionListener>
        <el-badge :value="executionListenerLength">
          <el-button size="small" @click="dialogName = 'executionListenerDialog'">编辑</el-button>
        </el-badge>
      </template>
      <template #signal>
        <el-badge :value="signalLength">
          <el-button size="small" @click="dialogName = 'signalDialog'">编辑</el-button>
        </el-badge>
      </template>
    </x-form>
    <div class="codeselect">
      <div class="leftselectname">事件类型</div>
      <div class="rightselectarea">
        <a-tree-select
          v-model="itemCode"
          style="width: 100%"
          :replaceFields="replaceFields"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="treeData"
          placeholder="请选择事件类型"
          @change="onChange"
        />
        <!-- <j-tree-select
          ref="jTreeSelect"
          placeholder="请选择事件编码"
          v-model="formData.itemCode"
          dict="v_event_item,item_name,item_code"
          pid-field="parent_code"
          pidValue="0"
          @change="onChange($event)"
        >
        </j-tree-select> -->
      </div>
    </div>
    <executionListenerDialog
      v-if="dialogName === 'executionListenerDialog'"
      :element="element"
      :modeler="modeler"
      @close="finishExecutionListener"
    />
    <signalDialog
      v-if="dialogName === 'signalDialog'"
      :element="element"
      :modeler="modeler"
      @close="finishExecutionListener"
    />
  </div>
</template>

<script>
import mixinPanel from '../../common/mixinPanel'
import mixinExecutionListener from '../../common/mixinExecutionListener'
import signalDialog from './property/signal'
import { commonParse } from '../../common/parseElement'
import { ajaxGetDictItems } from '@/api/api'
import { getAction } from '@/api/manage'
export default {
  components: {
    signalDialog,
  },
  mixins: [mixinPanel, mixinExecutionListener],
  data() {
    return {
      itemCode: '',
      treeData: [],
      limitThresholdTypes: [],
      signalLength: 0,
      formData: {},
      replaceFields: {
        children: 'children',
        title: 'label',
        key: 'value',
        value: 'code',
      },
    }
  },
  computed: {
    formConfig() {
      const _this = this
      return {
        inline: false,
        item: [
          // {
          //   xType: 'select',
          //   name: 'processCategory',
          //   label: '流程分类',
          //   dic: { data: _this.categorys, label: 'dictLabel', value: 'dictValue' }
          // },
          {
            xType: 'input',
            name: 'id',
            label: '流程标识key',
            width: 100,
            rules: [{ required: true, message: 'Id 不能为空' }],
          },
          {
            xType: 'input',
            name: 'name',
            label: '流程名称',
          },
          {
            xType: 'input',
            name: 'documentation',
            label: '节点描述',
          },
          // {
          //   xType: 'input',
          //   name: 'limitThresholdType',
          //   label: '时限设置（小时）',
          //   dic: _this.limitThresholdTypes,
          //   rules: [{ required: true, message: '时限设置不能为空' }],
          // },
          {
            xType: 'input',
            name: 'limitTime',
            label: '时限（小时）',
            rules: [{ required: true, message: '时限不能为空' }],
          },
          {
            xType: 'input',
            name: 'yellowLimitThreshold',
            label: '黄色预警阈值（小时）',
            rules: [{ required: true, message: '黄色预警阈值不能为空' }],
          },
          {
            xType: 'input',
            name: 'redLimitThreshold',
            label: '红色预警阈值（系数）',
            rules: [{ required: true, message: '红色预警阈值不能为空' }],
          },
          // {
          //   xType: 'slot',
          //   name: 'executionListener',
          //   label: '执行监听器'
          // },
          // {
          //   xType: 'slot',
          //   name: 'signal',
          //   label: '信号定义'
          // }
        ],
      }
    },
  },
  watch: {
    'formData.processCategory': function (val) {
      if (val === '') val = null
      this.updateProperties({ 'flowable:processCategory': val })
    },
  },
  created() {
    this.getItems()
    this.formData = commonParse(this.element)
  },

  methods: {
    onChange(e) {
      this.itemCode = e
      this.formData.itemCode = e
      this.$bus.$emit('eventType', this.itemCode)
    },
    getItems() {
      getAction('/right/queryEventItemTree', { leaf: '1' }).then(res => {
        this.treeData = []
        if (res.success) {
          this.treeData = res.result
          if (this.treeData.length > 0 && this.treeData[0].value == '-1') {
            // this.treeData.forEach(item => {
            //   this.initDisabled(item)
            // })
            this.treeData[0].disabled = true
          }
          this.initLimit()
          this.initDict()
        }
      })
    },
    initLimit() {
      if (this.deployId) {
        getAction('/flowable/definition/listLimit/' + this.deployId).then(res => {
          if (res) {
            this.formData.limitThresholdType = res.result.limitThresholdType
            this.formData.limitTime = res.result.limitTime
            this.formData.yellowLimitThreshold = res.result.yellowLimitThreshold
            this.formData.redLimitThreshold = res.result.redLimitThreshold
            this.formData.itemCode = res.result.itemCode
            this.itemCode = res.result.itemCode
          }
        })
      }
    },
    initDict() {
      ajaxGetDictItems('limit_threshold_type', null).then(res => {
        if (res.success) {
          //                console.log(res.result);
          this.limitThresholdTypes = res.result
          this.limitThresholdTypes.forEach(item => {
            item.label = item.text
          })
        }
      })
    },
    computedSignalLength() {
      // this.signalLength = this.element.businessObject.extensionElements?.values?.length ?? 0
      if (this.element.businessObject.extensionElements) {
        if (this.element.businessObject.extensionElements.value) {
          this.signalLength = this.element.businessObject.extensionElements.value.length
        }
        this.signalLength = 0
      } else {
        this.signalLength = 0
      }
    },
    finishSignal() {
      if (this.dialogName === 'signalDialog') {
        this.computedSignalLength()
      }
      this.dialogName = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.codeselect {
  width: 100%;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  .leftselectname {
    width: 140px;
    text-align: right;
    font-weight: 700;
    padding-right: 15px;
    box-sizing: border-box;
    height: 36px;
    line-height: 36px;
    font-size: 13px;
    color: #606266;
    font-family: 'Microsoft YaHei';
  }
  .rightselectarea {
    width: 190px;
    height: 36px;
    ::v-deep .ant-select {
      height: 36px;
      .ant-select-selection {
        height: 36px;
        .ant-select-selection__rendered {
          height: 36px;
        }
      }
    }
  }
}
</style>
